"""
大模型服务
"""
import os
import logging
from typing import Optional, Dict, Any, AsyncGenerator
import httpx
from datetime import datetime

logger = logging.getLogger(__name__)

class LLMService:
    """大模型服务类"""
    
    def __init__(self):
        # 从环境变量获取配置
        self.api_key = os.getenv("OPENAI_API_KEY", "")
        self.base_url = os.getenv("OPENAI_BASE_URL", "https://api.siliconflow.cn/v1")
        self.model = os.getenv("LLM_MODEL", "Qwen/Qwen2.5-7B-Instruct")
        self.timeout = int(os.getenv("LLM_TIMEOUT", "30"))
        
        # 如果没有配置API密钥，使用模拟模式
        self.mock_mode = (not self.api_key or
                         self.api_key.strip() == "" or
                         self.api_key.strip() == "your_siliconflow_api_key_here")
        
        if self.mock_mode:
            logger.warning("未配置硅基流动API密钥，将使用模拟模式")
        else:
            logger.info(f"硅基流动服务已初始化，使用模型: {self.model}")
    
    async def get_response(self, message: str, conversation_id: Optional[str] = None) -> str:
        """获取大模型响应"""
        if self.mock_mode:
            return await self._get_mock_response(message)
        
        try:
            return await self._call_siliconflow_api(message, conversation_id)
        except Exception as e:
            logger.error(f"调用硅基流动API失败: {e}")
            return f"抱歉，我现在无法回复您的消息。错误信息: {str(e)}"
    
    async def get_stream_response(self, message: str, conversation_id: Optional[str] = None) -> AsyncGenerator[str, None]:
        """获取流式响应"""
        if self.mock_mode:
            async for chunk in self._get_mock_stream_response(message):
                yield chunk
        else:
            try:
                async for chunk in self._call_siliconflow_stream_api(message, conversation_id):
                    yield chunk
            except Exception as e:
                logger.error(f"调用硅基流动流式API失败: {e}")
                yield f"抱歉，我现在无法回复您的消息。错误信息: {str(e)}"
    
    async def _call_siliconflow_api(self, message: str, conversation_id: Optional[str] = None) -> str:
        """调用硅基流动API"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": self.model,
            "messages": [
                {
                    "role": "system",
                    "content": "你是一个有用的AI助手，请用中文回复用户的问题。回答要准确、有帮助且友好。"
                },
                {
                    "role": "user",
                    "content": message
                }
            ],
            "temperature": 0.7,
            "max_tokens": 1000
        }
        
        async with httpx.AsyncClient(timeout=self.timeout) as client:
            response = await client.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=payload
            )
            response.raise_for_status()
            
            result = response.json()
            return result["choices"][0]["message"]["content"]
    
    async def _call_siliconflow_stream_api(self, message: str, conversation_id: Optional[str] = None) -> AsyncGenerator[str, None]:
        """调用硅基流动流式API"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": self.model,
            "messages": [
                {
                    "role": "system",
                    "content": "你是一个有用的AI助手，请用中文回复用户的问题。回答要准确、有帮助且友好。"
                },
                {
                    "role": "user",
                    "content": message
                }
            ],
            "temperature": 0.7,
            "max_tokens": 1000,
            "stream": True
        }
        
        async with httpx.AsyncClient(timeout=self.timeout) as client:
            async with client.stream(
                "POST",
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=payload
            ) as response:
                response.raise_for_status()
                
                async for line in response.aiter_lines():
                    if line.startswith("data: "):
                        data = line[6:]  # 移除 "data: " 前缀
                        if data == "[DONE]":
                            break
                        
                        try:
                            import json
                            chunk_data = json.loads(data)
                            if "choices" in chunk_data and len(chunk_data["choices"]) > 0:
                                delta = chunk_data["choices"][0].get("delta", {})
                                if "content" in delta:
                                    yield delta["content"]
                        except json.JSONDecodeError:
                            continue
    
    async def _get_mock_response(self, message: str) -> str:
        """获取模拟响应"""
        # 模拟一些延迟
        import asyncio
        await asyncio.sleep(1)

        # 智能模拟回复
        message_lower = message.lower()

        # 根据关键词生成更智能的回复
        if any(word in message_lower for word in ['你好', 'hello', 'hi', '您好']):
            return "您好！我是AI助手，很高兴为您服务。有什么我可以帮助您的吗？\n\n💡 提示：当前为演示模式，您可以配置真实的大模型API密钥获得更智能的回复。"

        elif any(word in message_lower for word in ['帮助', 'help', '功能', '能做什么']):
            return """我可以帮助您：

🤖 **聊天对话** - 与您进行自然语言交流
📝 **文本处理** - 帮助您处理和分析文本
💡 **问题解答** - 回答各种问题和提供建议
🔧 **技术支持** - 提供编程和技术相关的帮助

当前为演示模式，配置真实API后功能会更加强大！"""

        elif any(word in message_lower for word in ['谢谢', 'thank', '感谢']):
            return "不客气！很高兴能帮助到您。如果还有其他问题，随时可以问我哦！😊"

        elif any(word in message_lower for word in ['再见', 'bye', '拜拜', '结束']):
            return "再见！期待下次与您的对话。祝您生活愉快！👋"

        elif '?' in message or '？' in message:
            return f"这是一个很好的问题：「{message}」\n\n我正在思考最佳的回答方式。在真实的AI模式下，我能提供更详细和准确的回答。\n\n您还有其他问题吗？"

        else:
            # 通用回复
            responses = [
                f"我理解您说的「{message}」，这确实是一个值得深入讨论的话题。",
                f"关于「{message}」，我有一些想法可以与您分享。",
                f"您提到的「{message}」很有意思，让我想到了相关的一些观点。",
                f"「{message}」这个话题很有价值，我们可以进一步探讨。"
            ]

            # 根据消息内容选择回复
            import hashlib
            hash_value = int(hashlib.md5(message.encode()).hexdigest(), 16)
            selected_response = responses[hash_value % len(responses)]

            return f"{selected_response}\n\n💡 当前为演示模式，配置硅基流动API密钥后，您将获得更智能、更准确的AI回复。"
    
    async def _get_mock_stream_response(self, message: str) -> AsyncGenerator[str, None]:
        """获取模拟流式响应"""
        import asyncio
        
        response = await self._get_mock_response(message)
        words = response.split()
        
        for word in words:
            yield word + " "
            await asyncio.sleep(0.1)  # 模拟流式输出的延迟
