"""
FastAPI 大模型聊天应用主入口
"""
from contextlib import asynccontextmanager
from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
import uvicorn
import os
import logging
from dotenv import load_dotenv

from .websocket.manager import ConnectionManager
from .websocket.handlers import MessageHandler
from .models.message import ChatMessage, MessageType
from .database.database import db_manager

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时初始化数据库
    try:
        await db_manager.init_db()
        logger.info("数据库初始化完成")
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        raise

    yield

    # 关闭时清理资源
    try:
        await db_manager.close()
        logger.info("数据库连接已关闭")
    except Exception as e:
        logger.error(f"关闭数据库连接失败: {e}")

# 创建FastAPI应用实例
app = FastAPI(
    title="大模型聊天API",
    description="基于FastAPI和WebSocket的大模型聊天应用",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# 配置CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173", "http://127.0.0.1:5173"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 创建连接管理器和消息处理器
manager = ConnectionManager()
message_handler = MessageHandler()

@app.get("/")
async def read_root():
    """根路径，返回API信息"""
    return {
        "message": "大模型聊天API服务",
        "version": "1.0.0",
        "docs": "/docs",
        "websocket": "/ws/{client_id}"
    }

@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {"status": "healthy", "service": "llm-chat-api"}

@app.websocket("/ws/{client_id}")
async def websocket_endpoint(websocket: WebSocket, client_id: str):
    """WebSocket连接端点"""
    await manager.connect(websocket, client_id)
    try:
        while True:
            # 接收客户端消息
            data = await websocket.receive_text()
            
            # 处理消息
            response = await message_handler.handle_message(data, client_id)
            
            # 发送响应给当前客户端
            await manager.send_personal_message(response, websocket)
            
            # 如果需要，可以广播给所有客户端
            # await manager.broadcast(f"用户 {client_id}: {data}")
            
    except WebSocketDisconnect:
        manager.disconnect(websocket, client_id)
        await manager.broadcast(f"用户 {client_id} 离开了聊天")

@app.get("/api/clients")
async def get_connected_clients():
    """获取当前连接的客户端列表"""
    return {"clients": list(manager.active_connections.keys())}

if __name__ == "__main__":
    # 开发环境启动配置
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
