"""
数据库模型定义
"""
from sqlalchemy import <PERSON>umn, Integer, String, Text, DateTime, Boolean, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime
import uuid

Base = declarative_base()

class ChatSession(Base):
    """聊天会话模型"""
    __tablename__ = "chat_sessions"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    title = Column(String(200), nullable=False, default="新会话")
    user_id = Column(String(100), nullable=False, default="default_user")
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_active = Column(Boolean, default=True)
    
    # 关联消息
    messages = relationship("ChatMessage", back_populates="session", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<ChatSession(id={self.id}, title={self.title})>"

class ChatMessage(Base):
    """聊天消息模型"""
    __tablename__ = "chat_messages"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    session_id = Column(String, ForeignKey("chat_sessions.id"), nullable=False)
    message_type = Column(String(20), nullable=False)  # user, assistant, system, error
    content = Column(Text, nullable=False)
    user_id = Column(String(100), nullable=False)
    model_name = Column(String(100), nullable=True)  # 使用的AI模型
    tokens_used = Column(Integer, nullable=True)  # 使用的token数量
    response_time = Column(Integer, nullable=True)  # 响应时间(毫秒)
    created_at = Column(DateTime, default=datetime.utcnow)
    extra_data = Column(Text, nullable=True)  # JSON格式的额外数据
    
    # 关联会话
    session = relationship("ChatSession", back_populates="messages")
    
    def __repr__(self):
        return f"<ChatMessage(id={self.id}, type={self.message_type}, content={self.content[:50]}...)>"

class UserPreference(Base):
    """用户偏好设置模型"""
    __tablename__ = "user_preferences"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String(100), nullable=False, unique=True)
    preferred_model = Column(String(100), nullable=True)
    theme = Column(String(20), default="light")  # light, dark
    language = Column(String(10), default="zh-CN")
    auto_save = Column(Boolean, default=True)
    max_history = Column(Integer, default=1000)  # 最大保存消息数
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f"<UserPreference(user_id={self.user_id}, model={self.preferred_model})>"
