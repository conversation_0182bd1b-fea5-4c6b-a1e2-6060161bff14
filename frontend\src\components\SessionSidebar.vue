<template>
  <div class="session-sidebar" :class="{ 'sidebar-open': isOpen }">
    <!-- 侧边栏遮罩 -->
    <div v-if="isOpen" class="sidebar-overlay" @click="closeSidebar"></div>
    
    <!-- 侧边栏内容 -->
    <div class="sidebar-content">
      <!-- 侧边栏头部 -->
      <div class="sidebar-header">
        <h2 class="sidebar-title">聊天记录</h2>
        <button class="close-btn" @click="closeSidebar">
          <span class="close-icon">×</span>
        </button>
      </div>

      <!-- 新建会话按钮 -->
      <div class="new-session-section">
        <button class="new-session-btn" @click="createNewSession">
          <span class="plus-icon">+</span>
          <span class="btn-text">新建会话</span>
        </button>
      </div>

      <!-- 会话列表 -->
      <div class="sessions-list">
        <div v-if="chatStore.isLoadingHistory" class="loading-sessions">
          <div class="loading-spinner"></div>
          <span class="loading-text">加载中...</span>
        </div>
        
        <div v-else-if="chatStore.sessions.length === 0" class="empty-sessions">
          <div class="empty-icon">💬</div>
          <p class="empty-text">暂无聊天记录</p>
        </div>
        
        <div v-else class="session-items">
          <div 
            v-for="session in chatStore.sessions" 
            :key="session.id"
            class="session-item"
            :class="{ 'session-active': session.id === chatStore.currentSessionId }"
            @click="switchToSession(session.id)"
          >
            <div class="session-info">
              <h3 class="session-title">{{ session.title }}</h3>
              <p class="session-preview" v-if="session.last_message">
                {{ session.last_message.content }}
              </p>
              <p class="session-time">
                {{ formatTime(session.updated_at) }}
              </p>
            </div>
            <div class="session-actions">
              <button 
                class="action-btn delete-btn" 
                @click.stop="deleteSession(session.id)"
                title="删除会话"
              >
                <span class="delete-icon">🗑️</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 侧边栏底部 -->
      <div class="sidebar-footer">
        <div class="user-info">
          <div class="user-avatar">
            <span class="avatar-text">{{ chatStore.currentUser.charAt(0).toUpperCase() || 'U' }}</span>
          </div>
          <div class="user-details">
            <span class="user-name">{{ chatStore.currentUser || '用户' }}</span>
            <span class="user-status">在线</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useChatStore } from '../stores/chat'

// Props
interface Props {
  isOpen: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  close: []
  sessionChanged: [sessionId: string]
}>()

// Store
const chatStore = useChatStore()

// 关闭侧边栏
const closeSidebar = () => {
  emit('close')
}

// 创建新会话
const createNewSession = async () => {
  try {
    await chatStore.createSession('新会话')
    emit('close')
  } catch (error) {
    console.error('创建会话失败:', error)
  }
}

// 切换到指定会话
const switchToSession = async (sessionId: string) => {
  try {
    // 先发出会话变更事件，让父组件处理
    emit('sessionChanged', sessionId)
    emit('close')
  } catch (error) {
    console.error('切换会话失败:', error)
  }
}

// 删除会话
const deleteSession = async (sessionId: string) => {
  if (confirm('确定要删除这个会话吗？')) {
    try {
      // TODO: 实现删除会话功能
      console.log('删除会话:', sessionId)
    } catch (error) {
      console.error('删除会话失败:', error)
    }
  }
}

// 格式化时间
const formatTime = (timeString: string) => {
  const date = new Date(timeString)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  // 小于1分钟
  if (diff < 60000) {
    return '刚刚'
  }
  
  // 小于1小时
  if (diff < 3600000) {
    return `${Math.floor(diff / 60000)}分钟前`
  }
  
  // 小于1天
  if (diff < 86400000) {
    return `${Math.floor(diff / 3600000)}小时前`
  }
  
  // 小于7天
  if (diff < 604800000) {
    return `${Math.floor(diff / 86400000)}天前`
  }
  
  // 超过7天显示具体日期
  return date.toLocaleDateString('zh-CN', {
    month: 'short',
    day: 'numeric'
  })
}

// 组件挂载时加载会话列表
onMounted(async () => {
  try {
    await chatStore.getSessions()
  } catch (error) {
    console.error('加载会话列表失败:', error)
  }
})
</script>

<style scoped>
.session-sidebar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  pointer-events: none;
  transition: all 0.3s ease;
}

.sidebar-open {
  pointer-events: auto;
}

.sidebar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.sidebar-open .sidebar-overlay {
  opacity: 1;
}

.sidebar-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 320px;
  height: 100%;
  background: #ffffff;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
  transform: translateX(-100%);
  transition: transform 0.3s ease;
  display: flex;
  flex-direction: column;
}

.sidebar-open .sidebar-content {
  transform: translateX(0);
}

.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid #e5e5e5;
  background: #f8f9fa;
}

.sidebar-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  color: #666;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.close-btn:hover {
  background: #e9ecef;
}

.new-session-section {
  padding: 16px 20px;
  border-bottom: 1px solid #e5e5e5;
}

.new-session-btn {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.new-session-btn:hover {
  background: #0056b3;
}

.plus-icon {
  font-size: 16px;
  font-weight: bold;
}

.sessions-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px 0;
}

.loading-sessions, .empty-sessions {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #666;
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #e5e5e5;
  border-top: 2px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-icon {
  font-size: 32px;
  margin-bottom: 12px;
}

.session-item {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  cursor: pointer;
  transition: background-color 0.2s;
  border-left: 3px solid transparent;
}

.session-item:hover {
  background: #f8f9fa;
}

.session-active {
  background: #e3f2fd;
  border-left-color: #007bff;
}

.session-info {
  flex: 1;
  min-width: 0;
}

.session-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin: 0 0 4px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.session-preview {
  font-size: 12px;
  color: #666;
  margin: 0 0 4px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.session-time {
  font-size: 11px;
  color: #999;
  margin: 0;
}

.session-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s;
}

.session-item:hover .session-actions {
  opacity: 1;
}

.action-btn {
  background: none;
  border: none;
  padding: 4px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.delete-btn:hover {
  background: #fee;
}

.sidebar-footer {
  padding: 16px 20px;
  border-top: 1px solid #e5e5e5;
  background: #f8f9fa;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: #007bff;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 500;
  font-size: 14px;
}

.user-details {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.user-status {
  font-size: 12px;
  color: #28a745;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .sidebar-content {
    width: 280px;
  }
}
</style>
