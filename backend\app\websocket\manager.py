"""
WebSocket连接管理器
"""
from fastapi import WebSocket
from typing import Dict, List
import json
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

class ConnectionManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        # 存储活跃连接：{client_id: websocket}
        self.active_connections: Dict[str, WebSocket] = {}
        # 存储连接时间：{client_id: datetime}
        self.connection_times: Dict[str, datetime] = {}
    
    async def connect(self, websocket: WebSocket, client_id: str):
        """接受WebSocket连接"""
        await websocket.accept()
        self.active_connections[client_id] = websocket
        self.connection_times[client_id] = datetime.now()
        logger.info(f"客户端 {client_id} 已连接，当前连接数: {len(self.active_connections)}")
    
    def disconnect(self, websocket: WebSocket, client_id: str):
        """断开WebSocket连接"""
        if client_id in self.active_connections:
            del self.active_connections[client_id]
            del self.connection_times[client_id]
            logger.info(f"客户端 {client_id} 已断开连接，当前连接数: {len(self.active_connections)}")
    
    async def send_personal_message(self, message: str, websocket: WebSocket):
        """发送个人消息"""
        try:
            await websocket.send_text(message)
        except Exception as e:
            logger.error(f"发送个人消息失败: {e}")
    
    async def send_personal_json(self, data: dict, websocket: WebSocket):
        """发送个人JSON消息"""
        try:
            await websocket.send_json(data)
        except Exception as e:
            logger.error(f"发送个人JSON消息失败: {e}")
    
    async def broadcast(self, message: str):
        """广播消息给所有连接的客户端"""
        if not self.active_connections:
            return
        
        disconnected_clients = []
        for client_id, websocket in self.active_connections.items():
            try:
                await websocket.send_text(message)
            except Exception as e:
                logger.error(f"向客户端 {client_id} 广播消息失败: {e}")
                disconnected_clients.append(client_id)
        
        # 清理断开的连接
        for client_id in disconnected_clients:
            if client_id in self.active_connections:
                del self.active_connections[client_id]
                del self.connection_times[client_id]
    
    async def broadcast_json(self, data: dict):
        """广播JSON消息给所有连接的客户端"""
        if not self.active_connections:
            return
        
        disconnected_clients = []
        for client_id, websocket in self.active_connections.items():
            try:
                await websocket.send_json(data)
            except Exception as e:
                logger.error(f"向客户端 {client_id} 广播JSON消息失败: {e}")
                disconnected_clients.append(client_id)
        
        # 清理断开的连接
        for client_id in disconnected_clients:
            if client_id in self.active_connections:
                del self.active_connections[client_id]
                del self.connection_times[client_id]
    
    async def send_to_client(self, client_id: str, message: str):
        """发送消息给指定客户端"""
        if client_id in self.active_connections:
            websocket = self.active_connections[client_id]
            try:
                await websocket.send_text(message)
                return True
            except Exception as e:
                logger.error(f"向客户端 {client_id} 发送消息失败: {e}")
                # 清理断开的连接
                self.disconnect(websocket, client_id)
                return False
        return False
    
    async def send_json_to_client(self, client_id: str, data: dict):
        """发送JSON消息给指定客户端"""
        if client_id in self.active_connections:
            websocket = self.active_connections[client_id]
            try:
                await websocket.send_json(data)
                return True
            except Exception as e:
                logger.error(f"向客户端 {client_id} 发送JSON消息失败: {e}")
                # 清理断开的连接
                self.disconnect(websocket, client_id)
                return False
        return False
    
    def get_connection_count(self) -> int:
        """获取当前连接数"""
        return len(self.active_connections)
    
    def get_connected_clients(self) -> List[str]:
        """获取所有连接的客户端ID列表"""
        return list(self.active_connections.keys())
    
    def is_client_connected(self, client_id: str) -> bool:
        """检查客户端是否已连接"""
        return client_id in self.active_connections
