<template>
  <div class="model-selector-compact">
    <div class="selector-wrapper" :class="{ 'is-open': isOpen }">
      <button
        class="selector-trigger"
        @click="toggleDropdown"
        :disabled="isLoading"
      >
        <div class="selected-model">
          <span class="model-name">{{ selectedModelInfo?.name || 'Qwen2.5-7B' }}</span>
          <span class="model-provider" v-if="selectedModelInfo?.provider">
            ({{ selectedModelInfo.provider || '硅基流动' }})
          </span>
        </div>
        <svg class="dropdown-icon" :class="{ 'is-rotated': isOpen }" viewBox="0 0 16 16">
          <path d="M4 6l4 4 4-4" stroke="currentColor" stroke-width="2" fill="none"/>
        </svg>
      </button>
      
      <div class="dropdown-menu" v-if="isOpen" @click.stop>
        <div class="dropdown-header">
          <span>选择AI模型</span>
        </div>
        
        <div class="model-list">
          <div 
            v-for="model in availableModels" 
            :key="model.id"
            class="model-option"
            :class="{ 'is-selected': model.id === selectedModel, 'is-disabled': !model.available }"
            @click="selectModel(model.id)"
          >
            <div class="model-info">
              <div class="model-name">{{ model.name }}</div>
              <div class="model-details">
                <span class="model-provider">{{ model.provider }}</span>
                <span class="model-cost" v-if="model.cost">{{ model.cost }}</span>
              </div>
            </div>
            
            <div class="model-status">
              <div class="status-indicator" :class="model.status"></div>
              <svg v-if="model.id === selectedModel" class="check-icon" viewBox="0 0 16 16">
                <path d="M13.5 3.5L6 11l-3.5-3.5" stroke="currentColor" stroke-width="2" fill="none"/>
              </svg>
            </div>
          </div>
        </div>
        
        <div class="dropdown-footer">
          <button class="manage-models-btn" @click="openModelSettings">
            ⚙️ 管理模型
          </button>
        </div>
      </div>
    </div>
    
    <!-- 点击外部关闭下拉菜单 -->
    <div v-if="isOpen" class="dropdown-overlay" @click="closeDropdown"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

const isOpen = ref(false)
const isLoading = ref(false)

// 简化的模型数据
const availableModels = ref([
  {
    id: 'qwen-7b',
    name: 'Qwen2.5-7B',
    provider: '硅基流动',
    cost: '免费',
    status: 'healthy',
    available: true
  },
  {
    id: 'qwen-14b',
    name: 'Qwen2.5-14B',
    provider: '硅基流动',
    cost: '低成本',
    status: 'healthy',
    available: true
  },
  {
    id: 'deepseek-v2.5',
    name: 'DeepSeek-V2.5',
    provider: '硅基流动',
    cost: '免费',
    status: 'healthy',
    available: true
  }
])

const selectedModel = ref('qwen-7b')

const selectedModelInfo = computed(() => {
  return availableModels.value.find(model => model.id === selectedModel.value)
})

const toggleDropdown = () => {
  if (isLoading.value) return
  isOpen.value = !isOpen.value
}

const closeDropdown = () => {
  isOpen.value = false
}

const selectModel = (modelId: string) => {
  if (isLoading.value) return

  const model = availableModels.value.find(m => m.id === modelId)
  if (!model || !model.available) return

  selectedModel.value = modelId
  console.log(`已切换到模型: ${model.name}`)
  closeDropdown()
}

const openModelSettings = () => {
  closeDropdown()
  console.log('打开模型设置')
}
</script>

<style scoped>
.model-selector-compact {
  position: relative;
  z-index: 10;
}

.selector-wrapper {
  position: relative;
}

.selector-trigger {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-width: 140px;
  height: 40px;
  padding: 0 12px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 14px;
  color: #495057;
}

.selector-trigger:hover {
  background: #e9ecef;
  border-color: #adb5bd;
}

.selector-trigger:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.selector-wrapper.is-open .selector-trigger {
  background: white;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.selected-model {
  flex: 1;
  text-align: left;
  overflow: hidden;
}

.model-name {
  font-weight: 500;
  color: #333;
}

.model-provider {
  font-size: 12px;
  color: #666;
  margin-left: 4px;
}

.dropdown-icon {
  width: 16px;
  height: 16px;
  color: #666;
  transition: transform 0.2s;
  flex-shrink: 0;
  margin-left: 8px;
}

.dropdown-icon.is-rotated {
  transform: rotate(180deg);
}

.dropdown-menu {
  position: absolute;
  top: calc(100% + 4px);
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  overflow: hidden;
}

.dropdown-header {
  padding: 12px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  font-size: 12px;
  font-weight: 600;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.model-list {
  max-height: 240px;
  overflow-y: auto;
}

.model-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.2s;
  border-bottom: 1px solid #f8f9fa;
}

.model-option:hover {
  background: #f8f9fa;
}

.model-option.is-selected {
  background: #e3f2fd;
}

.model-option.is-disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.model-info {
  flex: 1;
}

.model-option .model-name {
  font-weight: 500;
  color: #333;
  margin-bottom: 2px;
}

.model-details {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #666;
}

.model-cost {
  padding: 2px 6px;
  background: #e9ecef;
  border-radius: 4px;
  font-size: 11px;
}

.model-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #28a745;
}

.status-indicator.unhealthy {
  background: #dc3545;
}

.status-indicator.warning {
  background: #ffc107;
}

.check-icon {
  width: 16px;
  height: 16px;
  color: #007bff;
}

.dropdown-footer {
  padding: 8px;
  border-top: 1px solid #e9ecef;
  background: #f8f9fa;
}

.manage-models-btn {
  width: 100%;
  padding: 8px 12px;
  background: none;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  color: #666;
  transition: all 0.2s;
}

.manage-models-btn:hover {
  background: #e9ecef;
  color: #333;
}

.dropdown-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .selector-trigger {
    min-width: 120px;
    height: 36px;
    padding: 0 10px;
    font-size: 13px;
  }
  
  .dropdown-menu {
    left: -20px;
    right: -20px;
  }
  
  .model-list {
    max-height: 200px;
  }
}
</style>
