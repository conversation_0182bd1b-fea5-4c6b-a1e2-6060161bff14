# 大模型聊天窗口框架

基于 FastAPI + Vue3 的现代化大模型聊天应用框架，支持实时WebSocket通信和流式响应。

## 🚀 项目特性

- **现代化技术栈**: FastAPI + Vue3 + WebSocket
- **实时通信**: 基于WebSocket的双向实时通信
- **响应式设计**: Vue3 Composition API + 响应式UI
- **模块化架构**: 前后端分离，易于扩展和维护
- **大模型集成**: 支持各种大模型API接入
- **开发友好**: 热重载、自动重启、TypeScript支持

## 📁 项目结构

```
llm_project/
├── README.md                 # 项目说明文档
├── backend/                  # FastAPI后端服务
│   ├── app/
│   │   ├── __init__.py
│   │   ├── main.py          # FastAPI主应用
│   │   ├── websocket/       # WebSocket处理模块
│   │   │   ├── __init__.py
│   │   │   ├── manager.py   # 连接管理器
│   │   │   └── handlers.py  # 消息处理器
│   │   ├── models/          # 数据模型
│   │   │   ├── __init__.py
│   │   │   └── message.py   # 消息模型
│   │   └── services/        # 业务服务
│   │       ├── __init__.py
│   │       └── llm_service.py # 大模型服务
│   └── requirements.txt     # Python依赖
├── frontend/                # Vue3前端应用
│   ├── src/
│   │   ├── components/      # Vue组件
│   │   │   ├── ChatWindow.vue
│   │   │   ├── MessageList.vue
│   │   │   └── MessageInput.vue
│   │   ├── services/        # API服务
│   │   │   └── websocket.js
│   │   ├── stores/          # 状态管理
│   │   │   └── chat.js
│   │   ├── App.vue
│   │   └── main.js
│   ├── package.json
│   └── vite.config.js
└── .gitignore
```

## 🛠️ 技术栈

### 后端
- **FastAPI**: 现代化的Python Web框架
- **WebSocket**: 实时双向通信
- **Uvicorn**: ASGI服务器
- **Pydantic**: 数据验证和序列化

### 前端
- **Vue3**: 渐进式JavaScript框架
- **Vite**: 下一代前端构建工具
- **TypeScript**: 类型安全的JavaScript
- **WebSocket API**: 浏览器原生WebSocket支持

## 🚀 快速开始

### 环境要求
- Python 3.8+
- Node.js 16+
- npm 或 yarn

### 安装和运行

#### 1. 克隆项目
```bash
git clone <repository-url>
cd llm_project
```

#### 2. 启动后端服务
```bash
cd backend
pip install -r requirements.txt

# 配置环境变量（可选）
cp .env.example .env
# 编辑 .env 文件，添加您的大模型API密钥

# 启动服务
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

#### 3. 启动前端服务
```bash
cd frontend
npm install
npm run dev
```

#### 4. 访问应用
- 前端应用: http://localhost:5173
- 后端API文档: http://localhost:8000/docs
- 健康检查: http://localhost:8000/health

### 🎯 立即体验
1. 打开浏览器访问 http://localhost:5173
2. 等待WebSocket连接建立（状态显示为"已连接"）
3. 在输入框中输入消息，按回车发送
4. 享受与AI助手的对话！

> 💡 **提示**: 未配置API密钥时，系统会使用智能模拟模式，您仍然可以体验完整的聊天功能。

## 📖 开发指南

### WebSocket通信协议

#### 消息格式
```json
{
  "type": "message|system|error",
  "content": "消息内容",
  "timestamp": "2024-01-01T00:00:00Z",
  "user_id": "用户ID"
}
```

#### 连接流程
1. 客户端连接WebSocket端点: `ws://localhost:8000/ws/{client_id}`
2. 服务端接受连接并加入连接池
3. 客户端发送消息，服务端处理并广播
4. 大模型响应通过WebSocket流式返回

### 大模型集成

框架支持集成各种大模型API：
- OpenAI GPT系列
- Claude
- 文心一言
- 通义千问
- 自定义API

### 扩展开发

#### 添加新的消息类型
1. 在 `backend/app/models/message.py` 中定义新的消息模型
2. 在 `backend/app/websocket/handlers.py` 中添加处理逻辑
3. 在前端组件中添加对应的UI处理

#### 自定义大模型服务
1. 在 `backend/app/services/` 中创建新的服务文件
2. 实现统一的服务接口
3. 在主应用中注册新服务

## 🔧 配置说明

### 环境变量
创建 `.env` 文件配置环境变量：
```env
# 硅基流动API配置 (推荐)
OPENAI_API_KEY=your_siliconflow_api_key
OPENAI_BASE_URL=https://api.siliconflow.cn/v1
LLM_MODEL=Qwen/Qwen2.5-7B-Instruct

# 服务配置
HOST=0.0.0.0
PORT=8000
DEBUG=true
```

### 获取硅基流动API密钥
1. 访问 [硅基流动官网](https://siliconflow.cn/)
2. 注册并登录账户
3. 进入 [API密钥页面](https://cloud.siliconflow.cn/account/ak)
4. 创建新的API密钥
5. 将密钥配置到 `.env` 文件中

### 支持的模型
- `Qwen/Qwen2.5-7B-Instruct` (推荐，性价比高)
- `Qwen/Qwen2.5-14B-Instruct` (更强性能)
- `deepseek-ai/DeepSeek-V2.5` (DeepSeek模型)
- `meta-llama/Meta-Llama-3.1-8B-Instruct` (Llama模型)

### 开发模式
- 后端: `uvicorn app.main:app --reload`
- 前端: `npm run dev`

### 生产部署
- 后端: `uvicorn app.main:app --host 0.0.0.0 --port 8000`
- 前端: `npm run build && npm run preview`

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [FastAPI](https://fastapi.tiangolo.com/) - 现代化的Python Web框架
- [Vue.js](https://vuejs.org/) - 渐进式JavaScript框架
- [Vite](https://vitejs.dev/) - 下一代前端构建工具

---

**开始构建您的大模型聊天应用吧！** 🎉
