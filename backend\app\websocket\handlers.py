"""
WebSocket消息处理器
"""
import json
import logging
import time
from datetime import datetime
from typing import Dict, Any

from ..models.message import ChatMessage, MessageType, WebSocketMessage
from ..services.llm_service import LLMService
from ..services.message_service import MessageService

logger = logging.getLogger(__name__)

class MessageHandler:
    """消息处理器"""
    
    def __init__(self):
        self.llm_service = LLMService()
        self.message_service = MessageService()
        self.current_session_id = None
    
    async def handle_message(self, raw_message: str, client_id: str) -> str:
        """处理接收到的消息"""
        try:
            # 解析消息
            message_data = json.loads(raw_message)
            
            # 根据消息类型处理
            action = message_data.get("action", "chat")
            
            if action == "chat":
                return await self._handle_chat_message(message_data, client_id)
            elif action == "ping":
                return await self._handle_ping_message(client_id)
            elif action == "system":
                return await self._handle_system_message(message_data, client_id)
            elif action == "load_history":
                return await self._handle_load_history(message_data, client_id)
            elif action == "get_sessions":
                return await self._handle_get_sessions(client_id)
            elif action == "create_session":
                return await self._handle_create_session(message_data, client_id)
            else:
                return await self._handle_unknown_message(message_data, client_id)
                
        except json.JSONDecodeError:
            # 如果不是JSON格式，当作普通聊天消息处理
            return await self._handle_plain_text_message(raw_message, client_id)
        except Exception as e:
            logger.error(f"处理消息时发生错误: {e}")
            return await self._create_error_response(f"消息处理失败: {str(e)}")
    
    async def _handle_chat_message(self, message_data: Dict[str, Any], client_id: str) -> str:
        """处理聊天消息"""
        try:
            user_message = message_data.get("content", "")
            if not user_message.strip():
                return await self._create_error_response("消息内容不能为空")

            # 获取会话ID，优先使用前端传递的session_id
            session_id = message_data.get("session_id")
            if session_id:
                self.current_session_id = session_id
            elif not self.current_session_id:
                # 如果没有指定会话ID且当前也没有会话，创建默认会话
                self.current_session_id = await self.message_service.get_or_create_default_session(client_id)

            # 保存用户消息到数据库
            user_msg_id = await self.message_service.save_message(
                session_id=self.current_session_id,
                message_type=MessageType.USER.value,
                content=user_message,
                user_id=client_id
            )

            # 创建用户消息对象
            chat_message = ChatMessage(
                type=MessageType.USER,
                content=user_message,
                user_id=client_id,
                timestamp=datetime.now()
            )

            # 记录开始时间
            start_time = time.time()

            # 调用大模型服务获取回复
            llm_response = await self.llm_service.get_response(user_message)

            # 计算响应时间
            response_time = int((time.time() - start_time) * 1000)  # 毫秒

            # 保存AI回复到数据库
            ai_msg_id = await self.message_service.save_message(
                session_id=self.current_session_id,
                message_type=MessageType.ASSISTANT.value,
                content=llm_response,
                user_id="assistant",
                model_name="default",  # 可以从配置中获取
                response_time=response_time
            )

            # 创建AI回复消息对象
            ai_message = ChatMessage(
                type=MessageType.ASSISTANT,
                content=llm_response,
                user_id="assistant",
                timestamp=datetime.now()
            )

            # 返回响应
            response_data = {
                "action": "chat_response",
                "user_message": chat_message.model_dump(mode='json'),
                "ai_message": ai_message.model_dump(mode='json'),
                "timestamp": datetime.now().isoformat(),
                "session_id": self.current_session_id,
                "message_ids": {
                    "user_message_id": user_msg_id,
                    "ai_message_id": ai_msg_id
                }
            }

            return json.dumps(response_data, ensure_ascii=False, default=str)

        except Exception as e:
            logger.error(f"处理聊天消息时发生错误: {e}")
            return await self._create_error_response(f"聊天处理失败: {str(e)}")
    
    async def _handle_ping_message(self, client_id: str) -> str:
        """处理ping消息"""
        response_data = {
            "action": "pong",
            "client_id": client_id,
            "timestamp": datetime.now().isoformat(),
            "message": "连接正常"
        }
        return json.dumps(response_data, ensure_ascii=False, default=str)
    
    async def _handle_system_message(self, message_data: Dict[str, Any], client_id: str) -> str:
        """处理系统消息"""
        system_action = message_data.get("system_action", "")
        
        if system_action == "get_status":
            response_data = {
                "action": "system_response",
                "system_action": "status",
                "data": {
                    "client_id": client_id,
                    "status": "connected",
                    "timestamp": datetime.now().isoformat()
                }
            }
            return json.dumps(response_data, ensure_ascii=False, default=str)
        
        return await self._create_error_response(f"未知的系统操作: {system_action}")
    
    async def _handle_plain_text_message(self, message: str, client_id: str) -> str:
        """处理纯文本消息"""
        # 将纯文本消息转换为聊天消息格式处理
        message_data = {
            "action": "chat",
            "content": message
        }
        return await self._handle_chat_message(message_data, client_id)
    
    async def _handle_unknown_message(self, message_data: Dict[str, Any], client_id: str) -> str:
        """处理未知类型的消息"""
        action = message_data.get("action", "unknown")
        return await self._create_error_response(f"未知的消息类型: {action}")
    
    async def _handle_load_history(self, message_data: Dict[str, Any], client_id: str) -> str:
        """处理加载历史消息请求"""
        try:
            session_id = message_data.get("session_id")
            limit = message_data.get("limit", 50)
            offset = message_data.get("offset", 0)

            if not session_id:
                # 如果没有指定会话ID，获取默认会话
                session_id = await self.message_service.get_or_create_default_session(client_id)

            # 获取历史消息
            messages = await self.message_service.get_session_messages(session_id, limit, offset)

            response_data = {
                "action": "history_loaded",
                "session_id": session_id,
                "messages": messages,
                "timestamp": datetime.now().isoformat()
            }

            return json.dumps(response_data, ensure_ascii=False, default=str)

        except Exception as e:
            logger.error(f"加载历史消息失败: {e}")
            return await self._create_error_response(f"加载历史消息失败: {str(e)}")

    async def _handle_get_sessions(self, client_id: str) -> str:
        """处理获取会话列表请求"""
        try:
            sessions = await self.message_service.get_user_sessions(client_id)

            response_data = {
                "action": "sessions_loaded",
                "sessions": sessions,
                "timestamp": datetime.now().isoformat()
            }

            return json.dumps(response_data, ensure_ascii=False, default=str)

        except Exception as e:
            logger.error(f"获取会话列表失败: {e}")
            return await self._create_error_response(f"获取会话列表失败: {str(e)}")

    async def _handle_create_session(self, message_data: Dict[str, Any], client_id: str) -> str:
        """处理创建新会话请求"""
        try:
            title = message_data.get("title", "新会话")
            session_id = await self.message_service.create_session(client_id, title)

            # 更新当前会话ID
            self.current_session_id = session_id

            response_data = {
                "action": "session_created",
                "session_id": session_id,
                "title": title,
                "timestamp": datetime.now().isoformat()
            }

            return json.dumps(response_data, ensure_ascii=False, default=str)

        except Exception as e:
            logger.error(f"创建会话失败: {e}")
            return await self._create_error_response(f"创建会话失败: {str(e)}")

    async def _create_error_response(self, error_message: str) -> str:
        """创建错误响应"""
        error_data = {
            "action": "error",
            "error": error_message,
            "timestamp": datetime.now().isoformat()
        }
        return json.dumps(error_data, ensure_ascii=False, default=str)
