<template>
  <div class="mobile-chat-container">
    <!-- 历史会话侧边栏 -->
    <SessionSidebar
      :isOpen="sidebarOpen"
      @close="closeSidebar"
      @sessionChanged="onSessionChanged"
    />

    <!-- 顶部导航栏 -->
    <header class="mobile-header">
      <div class="header-left">
        <button class="nav-btn" @click="showMenu">
          <span class="menu-icon">☰</span>
        </button>
      </div>
      <div class="header-center">
        <h1 class="chat-title">{{ currentSessionTitle }}</h1>
      </div>
      <div class="header-right">
        <button class="nav-btn" @click="showMore">
          <span class="more-icon">⋯</span>
        </button>
      </div>
    </header>

    <!-- 聊天内容区域 -->
    <main class="chat-content" ref="chatContent">
      <!-- 欢迎界面 -->
      <div v-if="chatStore.messages.length === 0" class="welcome-screen">
        <div class="welcome-avatar">
          <div class="avatar-circle">
            <span class="avatar-emoji">🤖</span>
          </div>
        </div>

        <div class="welcome-greeting">
          <h2 class="greeting-title">嗨！您好</h2>
          <p class="greeting-subtitle">小慧～三百杯（黑/绿/奶茶），没什么事茶，正午避暑呢</p>
        </div>

        <div class="quick-actions">
          <p class="action-tip">你可以这样文字，请告诉我点击下图案例问题～</p>
          <div class="action-buttons">
            <button class="action-btn" @click="selectQuickQuestion('今天天气怎么样？')">
              <span class="btn-icon">🌤️</span>
              <span class="btn-text">今天天气</span>
            </button>
            <button class="action-btn" @click="selectQuickQuestion('帮我写一首诗')">
              <span class="btn-icon">✍️</span>
              <span class="btn-text">写诗助手</span>
            </button>
            <button class="action-btn" @click="selectQuickQuestion('推荐一些好书')">
              <span class="btn-icon">📚</span>
              <span class="btn-text">推荐好书</span>
            </button>
            <button class="action-btn" @click="selectQuickQuestion('小慧是谁？')">
              <span class="btn-icon">🤔</span>
              <span class="btn-text">小慧是谁</span>
            </button>
          </div>
        </div>
      </div>

      <!-- 消息列表 -->
      <div v-else class="messages-area">
        <MessageList
          :messages="chatStore.messages"
          :is-typing="chatStore.isTyping"
        />
      </div>
    </main>

    <!-- 底部输入区域 -->
    <footer class="mobile-input-area">
      <div class="input-container">
        <div class="input-wrapper">
          <input
            v-model="inputMessage"
            @keydown="handleKeyDown"
            placeholder="请输入你的问题"
            class="message-input"
            ref="messageInput"
            :disabled="chatStore.isTyping"
          />
          <button
            @click="sendMessage"
            :disabled="!canSend"
            class="send-button"
            :class="{ active: canSend }"
          >
            <span class="send-icon">➤</span>
          </button>
        </div>
      </div>
    </footer>

    <!-- 连接状态覆盖层 -->
    <div v-if="chatStore.isConnecting" class="connection-overlay">
      <div class="connection-notice">
        <div class="loading-spinner"></div>
        <p>连接中...</p>
      </div>
    </div>

    <!-- 错误提示 -->
    <div v-if="chatStore.error" class="error-toast" @click="chatStore.clearError()">
      <div class="error-content">
        <span class="error-icon">⚠️</span>
        <span class="error-text">{{ chatStore.error }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { useChatStore } from '../stores/chat'
import MessageList from './MessageList.vue'
import SessionSidebar from './SessionSidebar.vue'

const chatStore = useChatStore()
const chatContent = ref<HTMLElement>()
const messageInput = ref<HTMLInputElement>()
const inputMessage = ref('')

// 侧边栏状态
const sidebarOpen = ref(false)

// 计算属性
const canSend = computed(() => {
  return inputMessage.value.trim() !== '' && !chatStore.isTyping
})

// 当前会话标题
const currentSessionTitle = computed(() => {
  if (chatStore.currentSessionId && chatStore.sessions.length > 0) {
    const currentSession = chatStore.sessions.find(s => s.id === chatStore.currentSessionId)
    return currentSession?.title || '新会话'
  }
  return '新会话'
})

// 菜单和更多操作
const showMenu = () => {
  sidebarOpen.value = true
}

const closeSidebar = () => {
  sidebarOpen.value = false
}

const onSessionChanged = async (sessionId: string) => {
  console.log('切换到会话:', sessionId)
  try {
    // 等待会话切换完成
    await chatStore.switchSession(sessionId)
    // 会话切换后滚动到底部
    nextTick(() => {
      scrollToBottom()
    })
  } catch (error) {
    console.error('切换会话失败:', error)
  }
}

const showMore = () => {
  console.log('显示更多选项')
}

// 快速问题选择
const selectQuickQuestion = (question: string) => {
  inputMessage.value = question
  sendMessage()
}

// 发送消息
const sendMessage = async () => {
  if (!canSend.value) return

  const message = inputMessage.value.trim()
  inputMessage.value = ''

  // 如果未连接，先初始化连接
  if (!chatStore.isConnected) {
    try {
      await chatStore.initializeConnection()
    } catch (error) {
      console.error('初始化连接失败:', error)
      return
    }
  }

  await chatStore.sendMessage(message)
  scrollToBottom()
}

// 处理键盘事件
const handleKeyDown = (event: KeyboardEvent) => {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()
    sendMessage()
  }
}

// 滚动到底部
const scrollToBottom = async () => {
  await nextTick()
  // 等待DOM完全更新
  await new Promise(resolve => setTimeout(resolve, 100))

  if (chatContent.value) {
    // 方法1：强制滚动到最大位置
    const scrollHeight = chatContent.value.scrollHeight
    chatContent.value.scrollTop = scrollHeight

    // 方法2：额外延迟后再次滚动
    setTimeout(() => {
      if (chatContent.value) {
        chatContent.value.scrollTop = chatContent.value.scrollHeight
      }
    }, 100)

    // 方法3：使用scrollIntoView作为备用
    const lastMessage = chatContent.value.querySelector('.message-wrapper:last-child, .typing-indicator')
    if (lastMessage) {
      setTimeout(() => {
        lastMessage.scrollIntoView({ behavior: 'smooth', block: 'end', inline: 'nearest' })
      }, 150)
    }
  }
}

// 监听消息变化，自动滚动到底部
watch(
  () => chatStore.messages.length,
  () => {
    scrollToBottom()
  }
)

// 监听打字状态变化，自动滚动到底部
watch(
  () => chatStore.isTyping,
  () => {
    scrollToBottom()
  }
)

// 组件挂载时不立即连接，让用户先看到欢迎页面
onMounted(async () => {
  console.log('ChatWindow mounted, welcome screen ready')

  // 如果已经连接，尝试加载历史消息
  if (chatStore.isConnected) {
    try {
      await chatStore.loadHistory()
    } catch (error) {
      console.error('加载历史消息失败:', error)
    }
  }
})

// 组件卸载时断开连接
onUnmounted(() => {
  chatStore.disconnect()
})
</script>

<style scoped>
/* 移动端聊天容器 */
.mobile-chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  background: #f5f5f5;
  overflow: hidden;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* 顶部导航栏 */
.mobile-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 56px;
  background: #fff;
  border-bottom: 1px solid #e5e5e5;
  padding: 0 12px;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  flex-shrink: 0;
  box-sizing: border-box;
}

.header-left,
.header-right {
  width: 40px; /* 减少宽度从48px到40px */
  display: flex;
  justify-content: center;
  flex-shrink: 0; /* 防止被压缩 */
}

.header-center {
  flex: 1;
  text-align: center;
}

.nav-btn {
  width: 36px; /* 减少按钮尺寸从40px到36px */
  height: 36px;
  border: none;
  background: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
  padding: 0; /* 确保没有额外padding */
}

.nav-btn:hover {
  background: #f0f0f0;
}

.menu-icon,
.more-icon {
  font-size: 16px; /* 减少图标尺寸从18px到16px */
  color: #333;
  line-height: 1; /* 确保图标垂直居中 */
}

.chat-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

/* 聊天内容区域 */
.chat-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;
  padding-top: 56px; /* 为固定导航栏留出空间 */
  padding-bottom: 180px; /* 大幅增加底部空间，彻底解决消息被遮挡问题 */
  background: #f5f5f5;
  min-height: calc(100vh - 56px); /* 确保最小高度 */
  width: 100%; /* 确保宽度铺满 */
  box-sizing: border-box; /* 确保padding计算正确 */
}

/* 欢迎界面 */
.welcome-screen {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: calc(100vh - 136px); /* 56px导航栏 + 80px输入框 */
  padding: 20px;
  text-align: center;
  margin-top: 0; /* 确保没有额外边距 */
  width: 100%;
  box-sizing: border-box;
}

.welcome-avatar {
  margin-bottom: 24px;
}

.avatar-circle {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
}

.avatar-emoji {
  font-size: 36px;
}

.welcome-greeting {
  margin-bottom: 32px;
}

.greeting-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.greeting-subtitle {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  max-width: 280px;
}

.quick-actions {
  width: 100%;
  max-width: 320px;
}

.action-tip {
  font-size: 14px;
  color: #666;
  margin-bottom: 20px;
  line-height: 1.5;
}

.action-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px 12px;
  background: #fff;
  border: 1px solid #e5e5e5;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s;
  min-height: 80px;
}

.action-btn:hover {
  background: #f8f9fa;
  border-color: #667eea;
  transform: translateY(-1px);
}

.btn-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.btn-text {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

/* 消息区域 */
.messages-area {
  width: 100%; /* 确保铺满宽度 */
  height: 100%; /* 确保铺满高度 */
  padding: 0; /* 移除padding，让MessageList自己处理 */
}

/* 底部输入区域 */
.mobile-input-area {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  border-top: 1px solid #e5e5e5;
  padding: 12px 16px;
  z-index: 100;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

.input-container {
  width: 100%;
}

.input-wrapper {
  display: flex;
  align-items: center;
  gap: 12px;
  background: #f8f9fa;
  border-radius: 24px;
  padding: 8px 16px;
  border: 1px solid #e5e5e5;
  transition: border-color 0.2s;
}

.input-wrapper:focus-within {
  border-color: #667eea;
}

.message-input {
  flex: 1;
  border: none;
  background: none;
  outline: none;
  font-size: 16px;
  color: #333;
  padding: 8px 0;
  resize: none;
}

.message-input::placeholder {
  color: #999;
}

.send-button {
  width: 36px;
  height: 36px;
  border: none;
  background: #e5e5e5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  flex-shrink: 0;
}

.send-button.active {
  background: #667eea;
  color: white;
}

.send-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.send-icon {
  font-size: 16px;
  transform: rotate(0deg);
  transition: transform 0.2s;
}

.send-button.active .send-icon {
  transform: rotate(0deg);
}

/* 连接状态覆盖层 */
.connection-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.connection-notice {
  background: #fff;
  padding: 24px;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 错误提示 */
.error-toast {
  position: fixed;
  top: 80px;
  left: 16px;
  right: 16px;
  background: #ff4757;
  color: white;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(255, 71, 87, 0.3);
  z-index: 1000;
  cursor: pointer;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.error-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.error-icon {
  font-size: 18px;
}

.error-text {
  flex: 1;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 480px) {
  /* 移动端导航栏优化 */
  .mobile-header {
    padding: 0 8px;
    height: 52px;
    width: 100%;
  }

  .chat-content {
    padding-top: 52px; /* 调整为移动端导航栏高度 */
    padding-bottom: 180px; /* 大幅增加移动端底部空间 */
  }

  .welcome-screen {
    min-height: calc(100vh - 132px); /* 52px导航栏 + 80px输入框 */
  }

  .header-left,
  .header-right {
    width: 36px; /* 进一步减少宽度 */
  }

  .nav-btn {
    width: 32px; /* 移动端更小的按钮 */
    height: 32px;
  }

  .menu-icon,
  .more-icon {
    font-size: 14px; /* 移动端更小的图标 */
  }

  .chat-title {
    font-size: 16px; /* 稍微减少标题字体 */
  }

  .action-buttons {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .action-btn {
    flex-direction: row;
    justify-content: flex-start;
    text-align: left;
    min-height: 60px;
    padding: 12px 16px;
  }

  .btn-icon {
    margin-bottom: 0;
    margin-right: 12px;
    font-size: 20px;
  }

  .btn-text {
    font-size: 14px;
  }

  .welcome-screen {
    padding: 20px 16px;
  }

  .greeting-title {
    font-size: 20px;
  }

  .greeting-subtitle {
    font-size: 13px;
  }

  .mobile-input-area {
    padding: 8px 12px;
  }

  .input-wrapper {
    padding: 6px 12px;
  }

  .message-input {
    font-size: 16px; /* 防止iOS缩放 */
  }
}

@media (max-width: 360px) {
  /* 小屏手机导航栏优化 */
  .mobile-header {
    padding: 0 6px;
    height: 48px;
    width: 100%;
  }

  .chat-content {
    padding-top: 48px; /* 调整为小屏导航栏高度 */
    padding-bottom: 180px; /* 大幅增加小屏幕底部空间 */
  }

  .welcome-screen {
    min-height: calc(100vh - 128px); /* 48px导航栏 + 80px输入框 */
  }

  .header-left,
  .header-right {
    width: 32px; /* 最小宽度 */
  }

  .nav-btn {
    width: 28px; /* 最小按钮尺寸 */
    height: 28px;
  }

  .menu-icon,
  .more-icon {
    font-size: 12px; /* 最小图标尺寸 */
  }

  .chat-title {
    font-size: 15px; /* 更小的标题 */
  }

  .welcome-screen {
    padding: 16px 12px;
  }

  .avatar-circle {
    width: 60px;
    height: 60px;
  }

  .avatar-emoji {
    font-size: 28px;
  }

  .action-btn {
    min-height: 50px;
    padding: 10px 12px;
  }

  .mobile-input-area {
    padding: 6px 8px;
  }
}
</style>
