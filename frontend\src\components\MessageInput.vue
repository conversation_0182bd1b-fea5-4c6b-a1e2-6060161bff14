<template>
  <div class="message-input">
    <div class="input-container">
      <!-- 简单的模型选择器 -->
      <div class="model-selector-simple">
        <select v-model="selectedModel" class="model-select">
          <option value="qwen-7b">Qwen2.5-7B</option>
          <option value="qwen-14b">Qwen2.5-14B</option>
          <option value="deepseek">DeepSeek-V2.5</option>
        </select>
      </div>

      <textarea
        ref="textareaRef"
        v-model="message"
        @keydown="handleKeydown"
        @input="adjustHeight"
        :placeholder="placeholder"
        :disabled="disabled"
        class="message-textarea"
        rows="1"
      ></textarea>

      <button
        @click="sendMessage"
        :disabled="!canSend"
        class="send-button"
        :class="{ 'sending': isSending }"
      >
        <svg v-if="!isSending" class="send-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <path d="m22 2-7 20-4-9-9-4 20-7z"/>
        </svg>
        <div v-else class="loading-spinner"></div>
      </button>
    </div>
    
    <div class="input-footer">
      <div class="input-hint">
        <span v-if="disabled" class="status-text error">未连接到服务器</span>
        <span v-else-if="isSending" class="status-text sending">发送中...</span>
        <span v-else class="status-text">按 Enter 发送，Shift + Enter 换行</span>
      </div>
      
      <div class="character-count" :class="{ 'warning': isNearLimit, 'error': isOverLimit }">
        {{ message.length }}/{{ maxLength }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, watch } from 'vue'

interface Props {
  disabled?: boolean
  isSending?: boolean
  placeholder?: string
  maxLength?: number
}

interface Emits {
  (e: 'send-message', message: string): void
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  isSending: false,
  placeholder: '输入消息...',
  maxLength: 2000
})

const emit = defineEmits<Emits>()

const message = ref('')
const textareaRef = ref<HTMLTextAreaElement>()
const selectedModel = ref('qwen-7b')

// 计算属性
const canSend = computed(() => {
  return !props.disabled && 
         !props.isSending && 
         message.value.trim().length > 0 && 
         message.value.length <= props.maxLength
})

const isNearLimit = computed(() => {
  return message.value.length > props.maxLength * 0.8
})

const isOverLimit = computed(() => {
  return message.value.length > props.maxLength
})

// 发送消息
const sendMessage = () => {
  if (!canSend.value) return
  
  const messageText = message.value.trim()
  if (messageText) {
    emit('send-message', messageText)
    message.value = ''
    adjustHeight()
  }
}

// 处理键盘事件
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Enter') {
    if (event.shiftKey) {
      // Shift + Enter: 换行
      return
    } else {
      // Enter: 发送消息
      event.preventDefault()
      sendMessage()
    }
  }
}

// 自动调整高度
const adjustHeight = () => {
  nextTick(() => {
    if (textareaRef.value) {
      textareaRef.value.style.height = 'auto'
      const scrollHeight = textareaRef.value.scrollHeight
      const maxHeight = 120 // 最大高度约5行
      textareaRef.value.style.height = Math.min(scrollHeight, maxHeight) + 'px'
    }
  })
}

// 监听发送状态变化，发送完成后聚焦输入框
watch(() => props.isSending, (newValue, oldValue) => {
  if (oldValue && !newValue) {
    // 发送完成，聚焦输入框
    nextTick(() => {
      textareaRef.value?.focus()
    })
  }
})

// 组件挂载后聚焦输入框
nextTick(() => {
  textareaRef.value?.focus()
})
</script>

<style scoped>
.message-input {
  padding: 16px 20px;
  background: #ffffff;
  border-top: 1px solid #e5e7eb;
}

.input-container {
  display: flex;
  align-items: flex-end;
  gap: 8px;
  padding: 8px;
  background: #f8fafc;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  transition: border-color 0.2s ease;
}

.model-selector-simple {
  flex-shrink: 0;
}

.model-select {
  min-width: 120px;
  height: 36px;
  padding: 0 12px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 18px;
  font-size: 14px;
  color: #495057;
  cursor: pointer;
  transition: all 0.2s;
}

.model-select:hover {
  background: #e9ecef;
  border-color: #adb5bd;
}

.model-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.25);
}

.input-container:focus-within {
  border-color: #667eea;
  background: #ffffff;
}

.message-textarea {
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  resize: none;
  font-size: 14px;
  line-height: 1.5;
  color: #1f2937;
  font-family: inherit;
  min-height: 20px;
  max-height: 120px;
  overflow-y: auto;
}

.message-textarea::placeholder {
  color: #9ca3af;
}

.message-textarea:disabled {
  color: #9ca3af;
  cursor: not-allowed;
}

.send-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 8px;
  background: #667eea;
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.send-button:hover:not(:disabled) {
  background: #5a67d8;
  transform: translateY(-1px);
}

.send-button:active:not(:disabled) {
  transform: translateY(0);
}

.send-button:disabled {
  background: #d1d5db;
  cursor: not-allowed;
  transform: none;
}

.send-button.sending {
  background: #fbbf24;
}

.send-icon {
  width: 18px;
  height: 18px;
  stroke-width: 2;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.input-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
  font-size: 12px;
}

.input-hint {
  color: #6b7280;
}

.status-text {
  transition: color 0.2s ease;
}

.status-text.error {
  color: #dc2626;
}

.status-text.sending {
  color: #f59e0b;
}

.character-count {
  color: #9ca3af;
  font-variant-numeric: tabular-nums;
  transition: color 0.2s ease;
}

.character-count.warning {
  color: #f59e0b;
}

.character-count.error {
  color: #dc2626;
  font-weight: 500;
}

/* 滚动条样式 */
.message-textarea::-webkit-scrollbar {
  width: 4px;
}

.message-textarea::-webkit-scrollbar-track {
  background: transparent;
}

.message-textarea::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 2px;
}

.message-textarea::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .message-input {
    padding: 12px 16px;
  }
  
  .input-container {
    padding: 8px;
    gap: 6px;
  }

  .model-select {
    min-width: 100px;
    height: 32px;
    padding: 0 10px;
    font-size: 13px;
    border-radius: 16px;
  }

  .send-button {
    width: 32px;
    height: 32px;
  }
  
  .send-icon {
    width: 16px;
    height: 16px;
  }
  
  .input-footer {
    font-size: 11px;
  }
}
</style>
