# 硅基流动API配置指南

## 🚀 快速配置步骤

### 1. 获取API密钥
1. 访问 [硅基流动官网](https://siliconflow.cn/)
2. 点击右上角"登录/注册"
3. 完成注册并登录
4. 进入 [API密钥管理页面](https://cloud.siliconflow.cn/account/ak)
5. 点击"新建API密钥"
6. 复制生成的API密钥

### 2. 配置API密钥
编辑 `backend/.env` 文件：

```env
# 硅基流动API配置 (请替换为您的真实API密钥)
OPENAI_API_KEY=sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
OPENAI_BASE_URL=https://api.siliconflow.cn/v1

# 推荐模型配置
LLM_MODEL=Qwen/Qwen2.5-7B-Instruct
```

### 3. 重启服务
- 后端会自动重载配置
- 前端无需重启

## 🎯 推荐模型选择

### 性价比首选
- `Qwen/Qwen2.5-7B-Instruct` - 速度快，成本低，适合日常对话

### 性能优先
- `Qwen/Qwen2.5-14B-Instruct` - 更强的理解和生成能力
- `Qwen/Qwen2.5-32B-Instruct` - 专业级性能

### 特色模型
- `deepseek-ai/DeepSeek-V2.5` - 代码生成专家
- `meta-llama/Meta-Llama-3.1-8B-Instruct` - Meta开源模型

## 💡 使用提示

### 成本控制
- 7B模型：约 ¥0.0007/1K tokens
- 14B模型：约 ¥0.0014/1K tokens
- 比OpenAI便宜90%以上

### 性能优化
- 国内访问速度快，无需翻墙
- 支持流式输出，体验流畅
- 完全兼容OpenAI API格式

## 🔧 故障排除

### 常见问题

**1. 401 Unauthorized错误**
- 检查API密钥是否正确
- 确认密钥没有多余空格
- 验证密钥是否已激活

**2. 模型不可用**
- 检查模型名称是否正确
- 确认账户有足够余额
- 尝试切换到其他模型

**3. 连接超时**
- 检查网络连接
- 确认防火墙设置
- 尝试重启服务

### 联系支持
- 官方文档：https://docs.siliconflow.cn/
- 技术支持：通过官网联系客服

## 📊 当前状态检查

### 模拟模式
如果看到"未配置硅基流动API密钥，将使用模拟模式"，说明：
- API密钥未配置或无效
- 系统将使用智能模拟回复
- 功能完整，但回复为预设内容

### 真实API模式
配置成功后会显示：
- "硅基流动服务已初始化，使用模型: xxx"
- 可以获得真正的AI智能回复
- 支持上下文对话和个性化回复

---

**配置完成后，您就可以享受高质量、低成本的AI聊天体验了！** 🎉
