"""
消息存储服务
"""
import json
import logging
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from sqlalchemy import select, desc, func, and_
from sqlalchemy.ext.asyncio import AsyncSession

from ..database.models import ChatSession, ChatMessage, UserPreference
from ..database.database import db_manager
from ..models.message import MessageType

logger = logging.getLogger(__name__)

class MessageService:
    """消息存储服务"""
    
    def __init__(self):
        self.db_manager = db_manager
    
    async def create_session(self, user_id: str = "default_user", title: str = "新会话") -> str:
        """创建新的聊天会话"""
        try:
            async with self.db_manager.get_session() as session:
                chat_session = ChatSession(
                    title=title,
                    user_id=user_id
                )
                session.add(chat_session)
                await session.commit()
                await session.refresh(chat_session)

                logger.info(f"创建新会话: {chat_session.id}")
                return chat_session.id
        except Exception as e:
            logger.error(f"创建会话失败: {e}")
            raise
    
    async def get_or_create_default_session(self, user_id: str = "default_user") -> str:
        """获取或创建默认会话"""
        try:
            async with self.db_manager.get_session() as session:
                # 查找用户的最新活跃会话
                stmt = select(ChatSession).where(
                    and_(
                        ChatSession.user_id == user_id,
                        ChatSession.is_active == True
                    )
                ).order_by(desc(ChatSession.updated_at)).limit(1)

                result = await session.execute(stmt)
                chat_session = result.scalar_one_or_none()

                if chat_session:
                    return chat_session.id
                else:
                    # 创建新会话
                    return await self.create_session(user_id, "默认会话")
        except Exception as e:
            logger.error(f"获取默认会话失败: {e}")
            # 如果出错，创建新会话
            return await self.create_session(user_id, "默认会话")
    
    async def save_message(
        self,
        session_id: str,
        message_type: str,
        content: str,
        user_id: str,
        model_name: Optional[str] = None,
        tokens_used: Optional[int] = None,
        response_time: Optional[int] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """保存消息到数据库"""
        try:
            async with self.db_manager.get_session() as db_session:
                message = ChatMessage(
                    session_id=session_id,
                    message_type=message_type,
                    content=content,
                    user_id=user_id,
                    model_name=model_name,
                    tokens_used=tokens_used,
                    response_time=response_time,
                    extra_data=json.dumps(metadata) if metadata else None
                )

                db_session.add(message)
                await db_session.commit()
                await db_session.refresh(message)

                # 更新会话的最后更新时间
                await self._update_session_timestamp(db_session, session_id)

                logger.debug(f"保存消息: {message.id}")
                return message.id
        except Exception as e:
            logger.error(f"保存消息失败: {e}")
            raise
    
    async def get_session_messages(
        self,
        session_id: str,
        limit: int = 50,
        offset: int = 0
    ) -> List[Dict[str, Any]]:
        """获取会话的消息历史"""
        try:
            async with self.db_manager.get_session() as session:
                stmt = select(ChatMessage).where(
                    ChatMessage.session_id == session_id
                ).order_by(ChatMessage.created_at).offset(offset).limit(limit)

                result = await session.execute(stmt)
                messages = result.scalars().all()

                return [
                    {
                        "id": msg.id,
                        "type": msg.message_type,
                        "content": msg.content,
                        "user_id": msg.user_id,
                        "model_name": msg.model_name,
                        "tokens_used": msg.tokens_used,
                        "response_time": msg.response_time,
                        "timestamp": msg.created_at.isoformat(),
                        "metadata": json.loads(msg.extra_data) if msg.extra_data else None
                    }
                    for msg in messages
                ]
        except Exception as e:
            logger.error(f"获取消息历史失败: {e}")
            return []
    
    async def get_user_sessions(
        self,
        user_id: str = "default_user",
        limit: int = 20
    ) -> List[Dict[str, Any]]:
        """获取用户的会话列表"""
        try:
            async with self.db_manager.get_session() as session:
                stmt = select(ChatSession).where(
                    and_(
                        ChatSession.user_id == user_id,
                        ChatSession.is_active == True
                    )
                ).order_by(desc(ChatSession.updated_at)).limit(limit)

                result = await session.execute(stmt)
                sessions = result.scalars().all()

                session_list = []
                for chat_session in sessions:
                    # 获取最后一条消息作为预览
                    last_msg_stmt = select(ChatMessage).where(
                        ChatMessage.session_id == chat_session.id
                    ).order_by(desc(ChatMessage.created_at)).limit(1)

                    last_msg_result = await session.execute(last_msg_stmt)
                    last_message = last_msg_result.scalar_one_or_none()

                    session_list.append({
                        "id": chat_session.id,
                        "title": chat_session.title,
                        "created_at": chat_session.created_at.isoformat(),
                        "updated_at": chat_session.updated_at.isoformat(),
                        "last_message": {
                            "content": last_message.content[:50] + "..." if last_message and len(last_message.content) > 50 else last_message.content if last_message else "",
                            "timestamp": last_message.created_at.isoformat() if last_message else chat_session.created_at.isoformat()
                        } if last_message else None
                    })

                return session_list
        except Exception as e:
            logger.error(f"获取用户会话失败: {e}")
            return []
    
    async def update_session_title(self, session_id: str, title: str) -> bool:
        """更新会话标题"""
        try:
            async with self.db_manager.get_session() as session:
                stmt = select(ChatSession).where(ChatSession.id == session_id)
                result = await session.execute(stmt)
                chat_session = result.scalar_one_or_none()

                if chat_session:
                    chat_session.title = title
                    chat_session.updated_at = datetime.utcnow()
                    await session.commit()
                    return True
                return False
        except Exception as e:
            logger.error(f"更新会话标题失败: {e}")
            return False
    
    async def delete_session(self, session_id: str) -> bool:
        """删除会话（软删除）"""
        try:
            async with self.db_manager.get_session() as session:
                stmt = select(ChatSession).where(ChatSession.id == session_id)
                result = await session.execute(stmt)
                chat_session = result.scalar_one_or_none()

                if chat_session:
                    chat_session.is_active = False
                    chat_session.updated_at = datetime.utcnow()
                    await session.commit()
                    return True
                return False
        except Exception as e:
            logger.error(f"删除会话失败: {e}")
            return False
    
    async def _update_session_timestamp(self, session: AsyncSession, session_id: str):
        """更新会话时间戳"""
        try:
            stmt = select(ChatSession).where(ChatSession.id == session_id)
            result = await session.execute(stmt)
            chat_session = result.scalar_one_or_none()
            
            if chat_session:
                chat_session.updated_at = datetime.utcnow()
                await session.commit()
        except Exception as e:
            logger.error(f"更新会话时间戳失败: {e}")
    
    async def get_message_stats(self, user_id: str = "default_user") -> Dict[str, Any]:
        """获取消息统计信息"""
        try:
            async with self.db_manager.get_session() as session:
                # 总消息数
                total_messages_stmt = select(func.count(ChatMessage.id)).join(
                    ChatSession
                ).where(ChatSession.user_id == user_id)
                total_messages = await session.scalar(total_messages_stmt)

                # 总会话数
                total_sessions_stmt = select(func.count(ChatSession.id)).where(
                    and_(
                        ChatSession.user_id == user_id,
                        ChatSession.is_active == True
                    )
                )
                total_sessions = await session.scalar(total_sessions_stmt)

                # 今日消息数
                today = datetime.utcnow().date()
                today_messages_stmt = select(func.count(ChatMessage.id)).join(
                    ChatSession
                ).where(
                    and_(
                        ChatSession.user_id == user_id,
                        func.date(ChatMessage.created_at) == today
                    )
                )
                today_messages = await session.scalar(today_messages_stmt)

                return {
                    "total_messages": total_messages or 0,
                    "total_sessions": total_sessions or 0,
                    "today_messages": today_messages or 0
                }
        except Exception as e:
            logger.error(f"获取消息统计失败: {e}")
            return {
                "total_messages": 0,
                "total_sessions": 0,
                "today_messages": 0
            }
