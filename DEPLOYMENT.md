# 部署指南

本文档介绍如何部署大模型聊天窗口框架到生产环境。

## 🚀 快速部署

### 环境要求
- Python 3.8+
- Node.js 16+
- 大模型API密钥（OpenAI、Claude、文心一言等）

### 1. 克隆项目
```bash
git clone <your-repo-url>
cd llm_project
```

### 2. 后端部署

#### 安装依赖
```bash
cd backend
pip install -r requirements.txt
```

#### 配置环境变量
```bash
cp .env.example .env
# 编辑 .env 文件，配置您的API密钥
```

#### 启动后端服务
```bash
# 开发环境
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# 生产环境
uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 4
```

### 3. 前端部署

#### 安装依赖
```bash
cd frontend
npm install
```

#### 开发环境
```bash
npm run dev
```

#### 生产环境
```bash
# 构建
npm run build

# 预览
npm run preview

# 或使用 nginx 等服务器托管 dist 目录
```

## 🐳 Docker 部署

### 创建 Dockerfile

#### 后端 Dockerfile
```dockerfile
FROM python:3.10-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

EXPOSE 8000

CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

#### 前端 Dockerfile
```dockerfile
FROM node:18-alpine as build

WORKDIR /app

COPY package*.json ./
RUN npm ci

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=build /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
```

### Docker Compose
```yaml
version: '3.8'

services:
  backend:
    build: ./backend
    ports:
      - "8000:8000"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENAI_BASE_URL=${OPENAI_BASE_URL}
    volumes:
      - ./backend:/app
    restart: unless-stopped

  frontend:
    build: ./frontend
    ports:
      - "80:80"
    depends_on:
      - backend
    restart: unless-stopped
```

## ☁️ 云平台部署

### Vercel (前端)
1. 连接 GitHub 仓库
2. 设置构建命令：`npm run build`
3. 设置输出目录：`dist`
4. 配置环境变量

### Railway/Render (后端)
1. 连接 GitHub 仓库
2. 选择 Python 环境
3. 设置启动命令：`uvicorn app.main:app --host 0.0.0.0 --port $PORT`
4. 配置环境变量

### AWS/阿里云 (完整部署)
1. 使用 ECS/EC2 实例
2. 配置负载均衡器
3. 设置 SSL 证书
4. 配置域名解析

## 🔧 生产环境配置

### 后端优化
```python
# app/main.py 生产环境配置
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        workers=4,  # 根据CPU核心数调整
        log_level="info",
        access_log=True
    )
```

### 前端优化
```javascript
// vite.config.ts 生产环境配置
export default defineConfig({
  plugins: [vue()],
  build: {
    minify: 'terser',
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'pinia'],
          utils: ['./src/services/websocket']
        }
      }
    }
  }
})
```

### Nginx 配置
```nginx
server {
    listen 80;
    server_name your-domain.com;

    # 前端静态文件
    location / {
        root /usr/share/nginx/html;
        try_files $uri $uri/ /index.html;
    }

    # API 代理
    location /api/ {
        proxy_pass http://backend:8000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    # WebSocket 代理
    location /ws/ {
        proxy_pass http://backend:8000/ws/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
    }
}
```

## 🔒 安全配置

### HTTPS 配置
```bash
# 使用 Let's Encrypt
certbot --nginx -d your-domain.com
```

### 环境变量安全
- 使用密钥管理服务
- 定期轮换API密钥
- 限制API访问权限

### 防火墙配置
```bash
# 只开放必要端口
ufw allow 22    # SSH
ufw allow 80    # HTTP
ufw allow 443   # HTTPS
ufw enable
```

## 📊 监控和日志

### 日志配置
```python
import logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('app.log'),
        logging.StreamHandler()
    ]
)
```

### 健康检查
```bash
# 添加健康检查端点
curl http://localhost:8000/health
```

## 🔄 CI/CD 配置

### GitHub Actions
```yaml
name: Deploy

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '18'
          
      - name: Setup Python
        uses: actions/setup-python@v2
        with:
          python-version: '3.10'
          
      - name: Deploy to production
        run: |
          # 部署脚本
```

## 📈 性能优化

### 后端优化
- 使用连接池
- 实现缓存机制
- 优化数据库查询
- 使用异步处理

### 前端优化
- 代码分割
- 懒加载组件
- 图片优化
- CDN 加速

## 🛠️ 故障排除

### 常见问题
1. **WebSocket 连接失败**
   - 检查防火墙设置
   - 确认代理配置
   - 验证SSL证书

2. **API 调用失败**
   - 检查API密钥
   - 确认网络连接
   - 查看错误日志

3. **前端加载慢**
   - 启用 gzip 压缩
   - 使用 CDN
   - 优化资源大小

### 日志查看
```bash
# 后端日志
tail -f app.log

# 前端日志
# 浏览器开发者工具 Console
```

---

需要帮助？请查看项目 README.md 或提交 Issue。
