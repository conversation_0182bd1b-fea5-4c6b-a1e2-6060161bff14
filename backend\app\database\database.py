"""
数据库配置和连接管理
"""
import os
import logging
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.orm import sessionmaker
from .models import Base

logger = logging.getLogger(__name__)

# 数据库配置
DATABASE_URL = os.getenv("DATABASE_URL", "sqlite+aiosqlite:///./chat_history.db")

# 创建异步引擎
engine = create_async_engine(
    DATABASE_URL,
    echo=os.getenv("DEBUG", "false").lower() == "true",  # 开发环境显示SQL
    future=True,
    pool_pre_ping=True,  # 连接池预检查
)

# 创建异步会话工厂
AsyncSessionLocal = async_sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False
)

async def init_database():
    """初始化数据库，创建所有表"""
    try:
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        logger.info("数据库初始化完成")
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        raise

async def get_db_session():
    """获取数据库会话 - 生成器函数"""
    async with AsyncSessionLocal() as session:
        try:
            yield session
        except Exception as e:
            await session.rollback()
            logger.error(f"数据库会话错误: {e}")
            raise
        finally:
            await session.close()

async def close_database():
    """关闭数据库连接"""
    try:
        await engine.dispose()
        logger.info("数据库连接已关闭")
    except Exception as e:
        logger.error(f"关闭数据库连接失败: {e}")

class DatabaseManager:
    """数据库管理器"""

    def __init__(self):
        self.engine = engine
        self.session_factory = AsyncSessionLocal

    def get_session(self):
        """获取数据库会话 - 返回异步上下文管理器"""
        return self.session_factory()

    async def init_db(self):
        """初始化数据库"""
        await init_database()

    async def close(self):
        """关闭数据库"""
        await close_database()

# 全局数据库管理器实例
db_manager = DatabaseManager()
